import { useState } from "react";
import { <PERSON><PERSON>, DialogContent } from "./ui/dialog";
import { Instagram, Linkedin, X } from "lucide-react";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import { <PERSON>mit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import useCustomToast from "./CustomToast";
import { ContactUsSlice } from "@/store/slices/contact-us";
import { z } from "zod";
import { LoaderButton } from "./ui-extended/loader-button";
import { Textarea } from "./ui/textarea";
import Checkbox from "./ui-extended/checkbox";
import Link from "next/link";
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectLabel,
	SelectTrigger,
	SelectValue,
} from "./ui/select";
import { SiX } from "react-icons/si";
import { countryCodes, InputPatterns } from "@/utils/constants";

const contactUsFormSchema = z.object({
	name: z.string().min(4, { message: "Full Name must be entered." }),
	email: z
		.string()
		.min(3, { message: "Email address is required" })
		.regex(InputPatterns.email, { message: "Invalid email address" }),
	company: z.string().optional(),
	phone: z
		.string()
		.optional()
		.refine(
			(value) => {
				if (!value || value.trim() === "") return true;
				return /^\d{10}$/.test(value);
			},
			{
				message: "Phone number must be at least 10 digits",
			}
		),
	// position: z.string().optional(),
	message: z
		.string()
		.min(5, { message: "Message must be at least 5 characters" }),
	agreeToTerms: z.boolean().refine((value) => value === true, {
		message: "You must agree to the terms and conditions",
	}),
});
export const GetInTouchModal: React.FC<{
	show: boolean;
	setShow: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ show, setShow }) => {
	const {
		register,
		handleSubmit,
		setError,
		reset,
		watch,
		setValue,
		formState: { errors, isDirty, isValid },
	} = useForm<z.infer<typeof contactUsFormSchema>>({
		resolver: zodResolver(contactUsFormSchema),
	});

	const [isChecked, setIsChecked] = useState(false);
	const customToast = useCustomToast();
	const [countryCode, setCountryCode] = useState("+1");
	const contactUsMutaion = ContactUsSlice(
		() => {
			setShow(false);
			setTimeout(() => {
				customToast("Message sent 😁, you would hear from us soon", {
					id: "contact-us",
					type: "success",
					duration: 5000,
				});
				reset();
			}, 1500);
		},
		() => {
			customToast("An error occured kindly try again later", {
				id: "contact-us",
				type: "error",
				duration: 5000,
			});
		}
	);

	const onSubmit: SubmitHandler<z.infer<typeof contactUsFormSchema>> = async (
		data
	) => {
		try {
			customToast("Sending message...", {
				id: "contact-us",
				type: "loading",
			});
			contactUsMutaion.mutate({
				name: data.name,
				email: data.email,
				company: data.company || "",
				phone_number:
					data.phone && data.phone.trim() !== ""
						? countryCode + data.phone
						: "",
				message: data.message,
			});
		} catch (error) {
			setError("root", {
				message: "An error occured kindly try again later",
			});
		}
	};
	return (
		<Dialog open={show} onOpenChange={setShow}>
			<DialogContent className="lg-optimal-height modal-lg-responsive w-full max-w-[1200px] border-[#C3C9D1] px-0 pb-16 pt-0 font-inter lg:rounded-2xl lg:!p-0 lg:pb-0">
				<div className="relative flex flex-col items-center justify-center lg:h-full lg:flex-row lg:bg-[#F4F5F7]">
					<button
						onClick={() => setShow(false)}
						className="absolute right-4 top-4 z-20 flex h-8 w-8 items-center justify-center rounded-full bg-[#E8EBEE] text-gray-400 transition-colors hover:text-gray-600"
					>
						<X className="h-5 w-5" />
					</button>
					<div className="flex h-full w-full flex-col justify-center border-b border-[#E4E4E7] bg-white p-8 pt-16 lg:w-1/2 lg:border-b-0 lg:pt-0">
						<div className="mx-auto w-full lg:max-w-[448px]">
							<p className="!font-sans text-sm font-medium text-[#01B18B] lg:text-base">
								Contact us
							</p>
							<h1 className="my-4 text-[30px] font-bold text-[#18181B] lg:text-[40px]">
								Get in Touch
							</h1>
							<p className="text-base leading-relaxed text-[#71717A] lg:text-lg">
								Got questions or need more information? We are
								here and happy to help! Please leave a message
								and we will get back to you shortly.
							</p>
							<div className="mt-6 flex space-x-8">
								<a
									href="https://www.instagram.com/migranium/"
									target="_blank"
									rel="noreferrer"
									title="Instagram"
								>
									<Instagram className="h-5 w-5 text-[#303741]" />
								</a>
								<a
									href="https://www.linkedin.com/company/migranium/"
									target="_blank"
									rel="noreferrer"
									title="Linkedin"
								>
									<Linkedin className="h-5 w-5 text-[#303741]" />
								</a>
								<a
									href="https://twitter.com/migranium"
									target="_blank"
									rel="noreferrer"
									title="Twitter"
								>
									<SiX className="h-5 w-5 text-[#303741]" />
								</a>{" "}
							</div>
						</div>
					</div>

					<div className="flex h-full w-full flex-col lg:w-1/2">
						<form
							onSubmit={handleSubmit(onSubmit)}
							className="form-fields-lg-responsive h-full flex-col pt-8 lg:flex lg:max-h-[calc(100dvh-80px)] lg:overflow-y-auto lg:scroll-smooth"
						>
							<div className="space-y-9 px-8 pb-4 lg:flex-1 lg:space-y-4 lg:px-[48px]">
								<div>
									<Label className="font-medium text-[#303741]">
										Full Name{" "}
										<span className="text-destructive">
											*
										</span>
									</Label>
									<Input
										className="border border-[#E4E4E7]"
										{...register("name")}
									/>
									{errors.name?.message && (
										<p className="mt-1.5 text-sm text-destructive">
											{errors.name?.message}
										</p>
									)}
								</div>

								<div>
									<Label className="font-medium text-[#303741]">
										Email{" "}
										<span className="text-destructive">
											*
										</span>
									</Label>
									<Input
										className="border border-[#E4E4E7]"
										{...register("email")}
									/>
									{errors.email?.message && (
										<p className="mt-1.5 text-sm text-destructive">
											{errors.email?.message}
										</p>
									)}
								</div>

								<div>
									<Label className="font-medium text-[#303741]">
										Organization (Optional)
									</Label>
									<Input
										className="border border-[#E4E4E7]"
										{...register("company")}
									/>
								</div>
								<div className="flex-1">
									<div className="flex-1 space-y-1.5">
										<Label className="text-[#323539]">
											Phone Number (Optional)
										</Label>
										<div className="flex">
											<Select
												value={countryCode}
												onValueChange={(value) => {
													setCountryCode(value);
												}}
											>
												<SelectTrigger className="w-fit rounded-r-none border-r-transparent">
													<SelectValue
														className="text-[#323539]"
														placeholder="+1"
													/>
												</SelectTrigger>
												<SelectContent className="!z-[9999]">
													<SelectGroup>
														<SelectLabel className="px-2">
															Country Codes
														</SelectLabel>

														{countryCodes.map(
															(option) => {
																return (
																	<SelectItem
																		key={
																			option.value
																		}
																		value={
																			option.value
																		}
																		className="px-8"
																	>
																		{
																			option.label
																		}
																	</SelectItem>
																);
															}
														)}
													</SelectGroup>
												</SelectContent>
											</Select>
											<Input
												type="tel"
												className="rounded-l-none border border-[#E4E4E7]"
												minLength={10}
												maxLength={10}
												{...register("phone", {
													minLength: 10,
													maxLength: 10,
												})}
											/>
										</div>
									</div>
									{errors.phone?.message && (
										<p className="mt-1.5 text-sm text-destructive">
											{errors.phone?.message}
										</p>
									)}
								</div>

								{/* Message */}
								<div className="mb-9">
									<Label className="font-medium text-[#303741]">
										Message{" "}
										<span className="text-destructive">
											*
										</span>
									</Label>
									<Textarea
										className="h-[95px] min-h-[75px] border border-[#E4E4E7] bg-white"
										{...register("message")}
									/>
									{errors.message?.message && (
										<p className="mt-1.5 text-sm text-destructive">
											{errors.message?.message}
										</p>
									)}
								</div>
							</div>

							<div className="space-y-3 px-8 lg:sticky lg:bottom-0 lg:z-10 lg:bg-[#F4F5F7] lg:px-[48px] lg:pb-8 lg:pt-4">
								<div>
									<div
										className="flex items-center space-x-3 self-start"
										onClick={() => {
											setIsChecked(!isChecked);
										}}
									>
										<Checkbox
											id={"check"}
											isChecked={watch("agreeToTerms")}
											handleCheckboxChange={() =>
												setValue(
													"agreeToTerms",
													!isChecked
												)
											}
											{...register("agreeToTerms")}
										/>
										<label className="text-sm text-[#68778D]">
											By selecting this you agree to our{" "}
											<Link
												href={"/privacy-policy"}
												className="text-[#043B6D] underline"
											>
												Privacy Policy.
											</Link>
										</label>
									</div>
									{errors.agreeToTerms?.message && (
										<p className="mt-1.5 text-sm !text-red-600">
											{errors.agreeToTerms?.message}
										</p>
									)}
								</div>
								<LoaderButton
									disabled={
										contactUsMutaion.isPending ||
										!isDirty ||
										!isValid
									}
									loading={contactUsMutaion.isPending}
									loaderSize={20}
									className="relative h-[42px] w-full self-end bg-[#043B6D] text-base font-medium text-white duration-200 ease-in-out hover:bg-[#72F4E8] hover:text-[#053969] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-[#043B6D] disabled:hover:text-white"
									type="submit"
								>
									Send Message
								</LoaderButton>
							</div>
						</form>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};
