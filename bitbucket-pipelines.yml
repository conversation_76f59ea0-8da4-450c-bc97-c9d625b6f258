image: node:20

pipelines:
  branches:
    dev:
      - step:
          name: Build & Deploy (LandingPage Dev)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - echo "Fetching Dev .env..."
            - curl -o .env "$DEV_AZURE_BLOB_SAS_URL"
            - export $(grep -v '^#' .env | xargs)
            - echo "Installing dependencies..."
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Building static site..."
            - npm run build
            - echo "Verifying build output..."
            - echo "Listing public folder:"
            - ls -la public/
            - echo "Listing out folder:"
            - ls -la out/
            - echo "Contents of out directory:"
            - find out/ -type f -name "*.html" | xargs cat
            - echo "Installing SWA CLI..."
            - npm install -g @azure/static-web-apps-cli
            - echo "Deploying out/ to migranium-dev…"
            - swa deploy out --app-name migranium-dev --deployment-token $LANDINGPAGE_DEV_SWA_TOKEN --env production --verbose

    staging:
      - step:
          name: Build & Deploy (LandingPage Staging)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - echo "Fetching Staging .env..."
            - curl -o .env "$STAGING_AZURE_BLOB_SAS_URL"
            - export $(grep -v '^#' .env | xargs)
            - echo "Installing dependencies..."
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Building static site..."
            - npm run build
            - echo "Verifying build output..."
            - echo "Listing public folder:"
            - ls -la public/
            - echo "Listing out folder:"
            - ls -la out/
            - echo "Contents of out directory:"
            - find out/ -type f -name "*.html" | xargs cat
            - echo "Installing SWA CLI..."
            - npm install -g @azure/static-web-apps-cli
            - echo "Deploying out/ to migranium-staging…"
            - swa deploy out --app-name migranium-staging --deployment-token $STAGING_SWA_TOKEN --env production --verbose

    main:
      - step:
          name: Build & Deploy (LandingPage Prod)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - echo "Fetching Prod .env..."
            - curl -o .env "$AZURE_BLOB_SAS_URL"
            - export $(grep -v '^#' .env | xargs)
            - echo "Installing dependencies..."
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Building static site..."
            - npm run build
            - echo "Verifying build output..."
            - echo "Listing public folder:"
            - ls -la public/
            - echo "Listing out folder:"
            - ls -la out/
            - echo "Contents of out directory:"
            - find out/ -type f -name "*.html" | xargs cat
            - echo "Installing SWA CLI..."
            - npm install -g @azure/static-web-apps-cli
            - echo "Deploying out/ to LandingPage…"
            - swa deploy out --app-name LandingPage --deployment-token $LANDINGPAGE_SWA_TOKEN --env production --verbose

  pull-requests:
    dev:
      - step:
          name: PR Build & Preview (LandingPage Dev)
          size: 2x
          caches:
            - node
          script:
            - export NODE_OPTIONS="--max_old_space_size=4096"
            - echo "Fetching PR .env..."
            - curl -o .env "$DEV_AZURE_BLOB_SAS_URL"
            - export $(grep -v '^#' .env | xargs)
            - echo "Installing dependencies..."
            - npm cache clean --force
            - rm -rf node_modules
            - npm install --legacy-peer-deps
            - echo "Building static site..."
            - npm run build
            - echo "Verifying build output..."
            - echo "Listing public folder:"
            - ls -la public/
            - echo "Listing out folder:"
            - ls -la out/
            - echo "Contents of out directory:"
            - find out/ -type f -name "*.html" | xargs cat
            - echo "Installing SWA CLI..."
            - npm install -g @azure/static-web-apps-cli
            - echo "Preview deploying out/ to migranium-dev…"
            - swa deploy out --app-name migranium-dev --deployment-token $LANDINGPAGE_DEV_SWA_TOKEN --env production --verbose